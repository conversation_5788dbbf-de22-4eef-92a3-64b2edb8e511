#!/usr/bin/env python3
"""
Main CLI application entry point
"""

import click
import sys
import os
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from .chat import ChatInterface
from .config import Config

console = Console()

@click.group(invoke_without_command=True)
@click.pass_context
@click.version_option(version="1.0.0")
def cli(ctx):
    """AI Assistant - Your intelligent command-line companion"""
    if ctx.invoked_subcommand is None:
        # If no subcommand is provided, start chat directly
        start_chat()

def start_chat():
    """Start the chat interface directly"""
    # Display welcome message
    welcome_text = Text("🤖 AI Assistant", style="bold cyan")
    welcome_panel = Panel(
        welcome_text,
        title="Welcome",
        border_style="cyan",
        padding=(1, 2)
    )
    console.print(welcome_panel)
    console.print()

    # Initialize configuration with defaults
    config = Config()

    # Start chat interface
    chat_interface = ChatInterface(config)
    chat_interface.start()

@cli.command()
def chat():
    """Start an interactive chat session with your AI assistant"""
    start_chat()

@cli.command()
def info():
    """Display information about the AI Assistant application"""
    info_text = """
    AI Assistant - Your Intelligent Command Line Companion

    Features:
    • Natural conversational AI chat
    • Code syntax highlighting
    • Token usage tracking
    • Multi-line input support
    • Command history
    • Ready to use - no setup required!

    Usage:
    Simply run: ai-cli

    That's it! No configuration needed.
    """

    console.print(Panel(info_text, title="AI Assistant Information", border_style="blue"))

def main():
    """Main entry point for the CLI application"""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye![/yellow]")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)

if __name__ == "__main__":
    main()