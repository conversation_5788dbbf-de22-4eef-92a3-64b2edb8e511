"""
Interactive chat interface for AI CLI
"""

import sys
import re
import time
import threading
from typing import List, Optional
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.markdown import Markdown
from rich.prompt import Prompt
from rich.live import Live
from rich.spinner import Spinner
from rich.layout import Layout
from rich.align import Align
from rich.columns import Columns
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from prompt_toolkit import prompt
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory
from prompt_toolkit.completion import WordCompleter
from prompt_toolkit.key_binding import KeyBindings
from prompt_toolkit.formatted_text import HTML

from .config import Config
from .api_client import OpenRouterClient, Message, APIError
from .utils import (
    extract_code_blocks,
    format_code_block,
    count_tokens,
    format_token_count,
    format_token_count_with_cost,
    get_real_time_token_count
)


class ChatInterface:
    """Interactive chat interface for the AI CLI"""

    def __init__(self, config: Config):
        self.config = config
        self.console = Console()
        self.client = OpenRouterClient(config)
        self.conversation_history: List[Message] = []
        self.history = InMemoryHistory()
        self.offline_mode = False
        self.demo_mode = not config.api_key or config.api_key == "demo"
        self.current_input = ""
        self.token_counter_active = True  # Always active now

        # Setup command completion
        self.completer = WordCompleter([
            '/help', '/clear', '/history', '/exit', '/quit', '/demo', '/tokens'
        ])

        # Setup key bindings for enhanced input
        self.key_bindings = KeyBindings()
        self._setup_key_bindings()

    def _setup_key_bindings(self):
        """Setup keyboard shortcuts"""
        @self.key_bindings.add('c-t')
        def toggle_token_counter(event):
            """Token counter is always active now - this is a no-op"""
            pass  # Token counter is always active

        @self.key_bindings.add('c-d')
        def toggle_demo_mode(event):
            """Toggle demo mode with Ctrl+D"""
            self.demo_mode = not self.demo_mode

    def start(self):
        """Start the interactive chat session"""
        # Display enhanced welcome message
        self._display_welcome()

        # Test API connection silently
        if not self.demo_mode and not self._test_connection_silently():
            return

        # Main chat loop
        while True:
            try:
                user_input = self._get_user_input_enhanced()

                if not user_input.strip():
                    continue

                # Handle commands
                if user_input.startswith('/'):
                    if self._handle_command(user_input):
                        continue
                    else:
                        break

                # Process user message
                self._process_user_message(user_input)

            except KeyboardInterrupt:
                self.console.print("\n[cyan]Thanks for chatting! Have a great day! 👋[/cyan]")
                break
            except Exception as e:
                self.console.print(f"[red]Error: {e}[/red]")

    def _test_connection_silently(self) -> bool:
        """Test the API connection silently"""
        try:
            success, message = self.client.test_connection()
            if success:
                return True
            else:
                # Silently switch to offline mode if connection fails
                self.offline_mode = True
                self.console.print("[yellow]Note: AI connection unavailable, using local responses.[/yellow]")
                self.console.print()
                return True
        except:
            # Fallback to offline mode
            self.offline_mode = True
            self.console.print("[yellow]Note: AI connection unavailable, using local responses.[/yellow]")
            self.console.print()
            return True

    def _get_user_input(self) -> str:
        """Get user input with history and completion"""
        try:
            return prompt(
                "You: ",
                history=self.history,
                auto_suggest=AutoSuggestFromHistory(),
                completer=self.completer,
                complete_style="column"
            )
        except (EOFError, KeyboardInterrupt):
            raise KeyboardInterrupt

    def _display_welcome(self):
        """Display enhanced welcome message with Claude-like styling"""
        if self.demo_mode:
            mode_text = Text("🎭 Demo Mode", style="bold yellow")
            mode_panel = Panel(
                Text("Running in demo mode - responses are simulated\nPress Ctrl+D to toggle demo mode",
                     style="yellow"),
                title=mode_text,
                border_style="yellow",
                padding=(0, 1)
            )
            self.console.print(mode_panel)
            self.console.print()

        welcome_text = Text("🤖 AI Assistant", style="bold cyan")
        welcome_content = Text.assemble(
            ("Welcome! I'm your AI assistant.\n", "cyan"),
            ("• Type ", "dim"),
            ("/help", "bold"),
            (" for commands\n", "dim"),
            ("• Token counter is ", "dim"),
            ("always active", "bold green"),
            ("\n", "dim"),
            ("• Press ", "dim"),
            ("Ctrl+C", "bold"),
            (" to exit anytime", "dim")
        )

        welcome_panel = Panel(
            welcome_content,
            title=welcome_text,
            border_style="cyan",
            padding=(1, 2)
        )
        self.console.print(welcome_panel)
        self.console.print()

    def _get_user_input_enhanced(self) -> str:
        """Get user input with enhanced features including real-time token counting"""
        try:
            # Token counter is always active now
            prompt_text = HTML('<cyan>You</cyan> <dim>(tokens always shown)</dim>: ')

            user_input = prompt(
                prompt_text,
                history=self.history,
                auto_suggest=AutoSuggestFromHistory(),
                completer=self.completer,
                complete_style="column",
                key_bindings=self.key_bindings
            )

            # Always display detailed token count for user input
            if user_input.strip():
                token_info = get_real_time_token_count(user_input, self.config.model)
                self.console.print(f"[dim]📊 Your message: {token_info['formatted']} | {token_info['words']} words | {token_info['characters']} characters[/dim]")

                # Show detailed word breakdown
                if token_info['word_breakdown']['breakdown_text']:
                    self.console.print(f"[dim]📝 Word breakdown: {token_info['word_breakdown']['breakdown_text']}[/dim]")

            return user_input

        except (EOFError, KeyboardInterrupt):
            raise KeyboardInterrupt

    def _handle_command(self, command: str) -> bool:
        """
        Handle chat commands

        Returns:
            True to continue chat, False to exit
        """
        command = command.lower().strip()

        if command in ['/exit', '/quit']:
            self.console.print("[cyan]Thanks for chatting! Take care! 👋[/cyan]")
            return False

        elif command == '/help':
            self._show_help()

        elif command == '/clear':
            self.conversation_history.clear()
            self.console.clear()
            self.console.print("[green]Fresh start! Our conversation history has been cleared.[/green]")

        elif command == '/history':
            self._show_history()

        elif command == '/demo':
            self.demo_mode = not self.demo_mode
            status = "enabled" if self.demo_mode else "disabled"
            self.console.print(f"[yellow]Demo mode {status}[/yellow]")

        elif command == '/tokens':
            self.console.print(f"[cyan]Token counter is always active[/cyan]")

        else:
            self.console.print(f"[yellow]I don't recognize that command: {command}[/yellow]")
            self.console.print("[cyan]Type '/help' to see what I can do![/cyan]")

        return True

    def _show_help(self):
        """Show help information"""
        help_text = """
Available Commands:
• /help     - Show this help message
• /clear    - Start fresh (clear our conversation)
• /history  - Review our conversation history
• /demo     - Toggle demo mode (simulated responses)
• /tokens   - Toggle real-time token counter
• /exit     - End our chat session
• /quit     - End our chat session

Keyboard Shortcuts:
• Ctrl+T    - Toggle token counter
• Ctrl+D    - Toggle demo mode
• Ctrl+C    - Exit anytime

Tips:
• Just chat naturally - I'm here to help!
• Ask me anything about coding, questions, or general topics
• Token counter shows real-time usage and estimated costs
• Demo mode provides simulated responses when API is unavailable
        """
        self.console.print(Panel(help_text, title="Help", border_style="blue"))

    def _show_history(self):
        """Show conversation history"""
        if not self.conversation_history:
            self.console.print("[yellow]We haven't started chatting yet! Say hello to begin our conversation.[/yellow]")
            return

        self.console.print(Panel("Our Conversation So Far", border_style="cyan"))
        for i, message in enumerate(self.conversation_history, 1):
            role_color = "green" if message.role == "user" else "blue"
            role_name = "You" if message.role == "user" else "AI"
            self.console.print(f"[{role_color}]{i}. {role_name}:[/{role_color}]")
            self.console.print(f"   {message.content[:100]}{'...' if len(message.content) > 100 else ''}")
            self.console.print()

    def _process_user_message(self, user_input: str):
        """Process user message and get AI response"""
        # Add user message to history
        user_message = Message(role="user", content=user_input)
        self.conversation_history.append(user_message)

        if self.demo_mode or self.offline_mode:
            # Demo/offline mode - provide helpful response with animation
            self._handle_offline_response_with_animation(user_input)
            return

        # Prepare messages for API (include conversation context)
        messages = self.conversation_history.copy()

        try:
            # Use enhanced animation system with timing
            ai_content, response_time = self._get_ai_response_with_animation(messages)

            if ai_content:
                ai_message = Message(role="assistant", content=ai_content)
                self.conversation_history.append(ai_message)

                # Display AI response with formatting and timing
                self._display_ai_response(ai_content, response_time)
            else:
                self.console.print("[red]Error: No response from AI[/red]")

        except APIError as e:
            self.console.print("[yellow]Switching to demo mode for now...[/yellow]")
            self.demo_mode = True
            self._handle_offline_response_with_animation(user_input)
        except Exception as e:
            self.console.print("[yellow]Switching to demo mode for now...[/yellow]")
            self.demo_mode = True
            self._handle_offline_response_with_animation(user_input)

    def _get_ai_response_with_animation(self, messages: List[Message]) -> tuple[Optional[str], float]:
        """Get AI response with 3-stage animation system and timing"""
        start_time = time.time()

        try:
            # Stage 1: AI is thinking (orange dots)
            with Live(self._create_thinking_animation(), console=self.console):
                time.sleep(1.5)  # Simulate thinking time
                response = self.client.chat_completion(messages)

            # Stage 2: AI is writing (blue spinner for code detection)
            if 'choices' in response and len(response['choices']) > 0:
                ai_content = response['choices'][0]['message']['content']

                # Check if response contains code
                has_code = '```' in ai_content or 'def ' in ai_content or 'function' in ai_content

                if has_code:
                    with Live(self._create_writing_code_animation(), console=self.console):
                        time.sleep(1.0)  # Simulate code writing

                # Stage 3: AI is testing (green progress for code responses)
                if has_code:
                    with Live(self._create_testing_animation(), console=self.console):
                        time.sleep(0.8)  # Simulate testing

                end_time = time.time()
                response_time = end_time - start_time
                return ai_content, response_time

            end_time = time.time()
            response_time = end_time - start_time
            return None, response_time

        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            raise e

    def _create_thinking_animation(self):
        """Create thinking animation (Stage 1) with spinner"""
        spinner = Spinner("dots", text="AI is thinking", style="bold orange1")
        return spinner

    def _create_writing_code_animation(self):
        """Create writing code animation (Stage 2) with spinner"""
        spinner = Spinner("line", text="AI is writing code", style="bold blue")
        return spinner

    def _create_testing_animation(self):
        """Create testing animation (Stage 3) with spinner"""
        spinner = Spinner("arc", text="AI is testing", style="bold green")
        return spinner

    def _handle_offline_response_with_animation(self, user_input: str):
        """Handle offline response with animation and timing"""
        import random

        start_time = time.time()

        # Simulate the 3-stage animation for demo mode
        with Live(self._create_thinking_animation(), console=self.console):
            time.sleep(1.2)

        # Determine if we should simulate code writing
        code_keywords = ['code', 'function', 'python', 'javascript', 'program', 'script', 'algorithm']
        has_code_request = any(keyword in user_input.lower() for keyword in code_keywords)

        if has_code_request:
            with Live(self._create_writing_code_animation(), console=self.console):
                time.sleep(1.0)
            with Live(self._create_testing_animation(), console=self.console):
                time.sleep(0.8)

        end_time = time.time()
        response_time = end_time - start_time

        # Generate demo response with timing
        self._handle_offline_response(user_input, response_time)

    def _display_ai_response(self, content: str, response_time: float = 0.0):
        """Display AI response with proper formatting and timing"""
        self.console.print()

        # Enhanced token display with timing information
        token_info = get_real_time_token_count(content, self.config.model)

        # Create enhanced header with token info and timing
        if self.demo_mode:
            header = Text.assemble(
                ("AI", "bold blue"),
                (" [Demo Mode] ", "yellow"),
                (f"({token_info['formatted']})", "dim")
            )
        else:
            header = Text.assemble(
                ("AI", "bold blue"),
                (f" ({token_info['formatted']})", "dim")
            )

        self.console.print(header)

        # Display response time
        if response_time > 0:
            self.console.print(f"[dim]⏱️  Response time: {response_time:.1f} seconds[/dim]")

        # Extract and format code blocks separately
        code_blocks = extract_code_blocks(content)

        if code_blocks:
            # If there are code blocks, process them specially
            remaining_content = content

            for code, language in code_blocks:
                # Remove the code block from content for markdown rendering
                code_pattern = f"```{language or ''}\n{re.escape(code)}\n```"
                remaining_content = re.sub(code_pattern, "[CODE_BLOCK_PLACEHOLDER]", remaining_content, count=1)

            # Render remaining content as markdown
            try:
                if remaining_content.strip() and remaining_content.strip() != "[CODE_BLOCK_PLACEHOLDER]":
                    # Replace placeholders and render
                    parts = remaining_content.split("[CODE_BLOCK_PLACEHOLDER]")

                    for i, part in enumerate(parts):
                        if part.strip():
                            markdown = Markdown(part)
                            self.console.print(markdown)

                        # Display code block if there's one after this part
                        if i < len(code_blocks):
                            code, language = code_blocks[i]
                            formatted_code = format_code_block(code, language)
                            self.console.print(formatted_code)
                else:
                    # Only code blocks, display them
                    for code, language in code_blocks:
                        formatted_code = format_code_block(code, language)
                        self.console.print(formatted_code)

            except Exception:
                # Fallback to plain text if markdown fails
                self.console.print(content)
        else:
            # No code blocks, render as markdown
            try:
                markdown = Markdown(content)
                self.console.print(markdown)
            except Exception:
                # Fallback to plain text if markdown fails
                self.console.print(content)

        self.console.print()

    def _handle_offline_response(self, user_input: str, response_time: float = 0.0):
        """Handle user input in offline mode with timing"""
        # Provide helpful offline responses
        offline_responses = {
            "hello": "Hello! I'm here to help you with coding questions and general assistance.",
            "help": "I can help you with:\n• Programming questions and code examples\n• Explanations and tutorials\n• General questions and conversations\n• Code syntax highlighting and formatting",
            "test": "This is a demo response! The AI assistant is working correctly.",
            "code": "Here's a sample Python function:\n\n```python\ndef hello_world():\n    print('Hello, World!')\n    return 'Success'\n```\n\nThis demonstrates the code highlighting feature!",
            "python": "Python is a great programming language! Here's a quick example:\n\n```python\n# Simple list comprehension\nnumbers = [x**2 for x in range(10)]\nprint(numbers)\n```",
            "javascript": "JavaScript is versatile! Here's an example:\n\n```javascript\n// Arrow function example\nconst greet = (name) => {\n    return `Hello, ${name}!`;\n};\nconsole.log(greet('World'));\n```"
        }

        # Simple keyword matching for offline responses
        user_lower = user_input.lower()
        response = None

        for keyword, reply in offline_responses.items():
            if keyword in user_lower:
                response = reply
                break

        if not response:
            # Generate more natural conversational responses
            import random
            user_lower = user_input.lower()

            # Handle common greetings naturally
            if any(greeting in user_lower for greeting in ['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening']):
                greetings = [
                    "Hello! How can I help you today?",
                    "Hi there! What can I assist you with?",
                    "Hello! I'm here to help with any questions you have.",
                    "Hi! What would you like to know?"
                ]
                response = random.choice(greetings)

            # Handle name questions
            elif any(phrase in user_lower for phrase in ['what is your name', 'who are you', 'what are you called']):
                name_responses = [
                    "I'm an AI assistant here to help you with coding, questions, and conversations.",
                    "I don't have a specific name, but you can call me Assistant. How can I help you?",
                    "I'm your AI coding assistant. What can I help you with today?"
                ]
                response = random.choice(name_responses)

            # Handle how are you questions
            elif any(phrase in user_lower for phrase in ['how are you', 'how do you do', 'how\'s it going']):
                status_responses = [
                    "I'm doing well, thank you! Ready to help with any questions you have.",
                    "I'm here and ready to assist! What can I help you with?",
                    "All good here! How can I help you today?"
                ]
                response = random.choice(status_responses)

            # Default helpful responses
            else:
                helpful_responses = [
                    f"I'd be happy to help you with '{user_input}'. Could you provide more details about what you're looking for?",
                    f"That's an interesting question about '{user_input}'. What specific aspect would you like to explore?",
                    "I'm here to help with coding questions, explanations, and general assistance. What would you like to know more about?",
                    "I can assist with programming questions, provide code examples, and explain concepts. What specific help do you need?"
                ]
                response = random.choice(helpful_responses)

        # Add to conversation history
        ai_message = Message(role="assistant", content=response)
        self.conversation_history.append(ai_message)

        # Display response with timing
        self._display_ai_response(response, response_time)